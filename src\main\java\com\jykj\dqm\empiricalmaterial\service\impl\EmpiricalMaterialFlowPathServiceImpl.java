package com.jykj.dqm.empiricalmaterial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jykj.dqm.common.R;
import com.jykj.dqm.common.RUtil;
import com.jykj.dqm.common.UploadImageFile;
import com.jykj.dqm.empiricalmaterial.dao.EmpiricalMaterialFlowPathMapper;
import com.jykj.dqm.empiricalmaterial.entity.EMImagesInfo;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialEvaluationContent;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowFirstTreeVO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPath;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathDTO;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowPathQuery;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialFlowSecondTree;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialTaskAllocation;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialEvaluationContentService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialExportService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialFlowPathService;
import com.jykj.dqm.empiricalmaterial.service.EmpiricalMaterialTaskAllocationService;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.emr.entity.RulePermissionConfiguration;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.IdUtils;
import com.jykj.dqm.utils.SystemUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH(实证材料流程和资料)】的数据库操作Service实现
 * @createDate 2024-01-23 14:25:03
 */
@Service
public class EmpiricalMaterialFlowPathServiceImpl extends ServiceImpl<EmpiricalMaterialFlowPathMapper, EmpiricalMaterialFlowPath> implements EmpiricalMaterialFlowPathService {
    @Autowired
    private EmpiricalMaterialEvaluationContentService empiricalMaterialEvaluationContentService;

    @Autowired
    private EmpiricalMaterialTaskAllocationService empiricalMaterialTaskAllocationService;

    @Autowired
    private EmpiricalMaterialExportService empiricalMaterialExportService;

    @Autowired
    private UploadImageFile uploadImageFile;

    @Override
    public EmpiricalMaterialFlowFirstTreeVO query(EmpiricalMaterialFlowPathQuery materialFlowPathQuery) {
        List<EmpiricalMaterialFlowPath> list = queryFlowPathInfo(materialFlowPathQuery, true);
        EmpiricalMaterialFlowFirstTreeVO materialFlowFirstTreeVO = new EmpiricalMaterialFlowFirstTreeVO();
        List<EmpiricalMaterialFlowPath> allFlowPath = list.stream().filter(empiricalMaterialFlowPath -> "0".equals(empiricalMaterialFlowPath.getFlowPathType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(allFlowPath)) {
            materialFlowFirstTreeVO.setAllFlowPath(allFlowPath.get(0));
        }
        List<EmpiricalMaterialFlowPath> parentIdEmpiricalMaterialFlowPaths = list.stream()
                .filter(empiricalMaterialFlowPath -> StrUtil.isBlank(empiricalMaterialFlowPath.getParentId()) && !"0".equals(empiricalMaterialFlowPath.getFlowPathType()))
                .collect(Collectors.toList());

        Map<String, List<EmpiricalMaterialFlowPath>> listMap = list.stream()
                .filter(empiricalMaterialFlowPath -> StrUtil.isNotBlank(empiricalMaterialFlowPath.getParentId()))
                .collect(Collectors.groupingBy(EmpiricalMaterialFlowPath::getParentId, LinkedHashMap::new, Collectors.toList()));
        List<EmpiricalMaterialFlowSecondTree> result = new ArrayList<>();
        EmpiricalMaterialFlowSecondTree empiricalMaterialFlowTreeVO;
        for (EmpiricalMaterialFlowPath materialFlowPath : parentIdEmpiricalMaterialFlowPaths) {
            empiricalMaterialFlowTreeVO = new EmpiricalMaterialFlowSecondTree();
            empiricalMaterialFlowTreeVO.setFlowPath(materialFlowPath.getFlowPath());
            empiricalMaterialFlowTreeVO.setFlowPathData(materialFlowPath.getFlowPathData());
            empiricalMaterialFlowTreeVO.setId(materialFlowPath.getId());
            empiricalMaterialFlowTreeVO.setChildren(listMap.get(materialFlowPath.getId()));
            result.add(empiricalMaterialFlowTreeVO);
        }
        materialFlowFirstTreeVO.setGroupFlowPath(result);
        return materialFlowFirstTreeVO;
    }

    public List<EmpiricalMaterialFlowPath> queryFlowPathInfo(EmpiricalMaterialFlowPathQuery materialFlowPathQuery, boolean needAllFlowPathType) {
        Assert.notBlank(materialFlowPathQuery.getEvaluationContentId(), "评价内容ID不能为空");
        List<EmpiricalMaterialFlowPath> list = this.list(
                Wrappers.lambdaQuery(EmpiricalMaterialFlowPath.class)
                        .eq(EmpiricalMaterialFlowPath::getProjectId, materialFlowPathQuery.getProjectId())
                        .eq(EmpiricalMaterialFlowPath::getDirectoryCode, materialFlowPathQuery.getDirectoryCode())
                        .eq(EmpiricalMaterialFlowPath::getDirectoryName, materialFlowPathQuery.getDirectoryName())
                        .eq(EmpiricalMaterialFlowPath::getEvaluationContentId, materialFlowPathQuery.getEvaluationContentId())
                        .eq(!needAllFlowPathType, EmpiricalMaterialFlowPath::getFlowPathType, "2")
                        .orderByAsc(EmpiricalMaterialFlowPath::getId)
        );
        String parent = SystemUtils.getFilePath();
        String directoryPath = materialFlowPathQuery.getDirectoryName() + materialFlowPathQuery.getDirectoryCode() + materialFlowPathQuery.getProjectId() + "/" + materialFlowPathQuery.getEvaluationContentId();
        //返回图片的压缩图
        StringBuilder serialNumBuilder;
        //获取evaluationContentCount
        EmpiricalMaterialEvaluationContent materialEvaluationContent =
                empiricalMaterialEvaluationContentService.getById(materialFlowPathQuery.getEvaluationContentId());
        int evaluationContentCount = Integer.parseInt(materialEvaluationContent.getSerialNum());
        int flowPathCount = 0;
        for (EmpiricalMaterialFlowPath flowPath : list) {
            serialNumBuilder = new StringBuilder();
            if (!"2".equals(flowPath.getFlowPathType()) || StrUtil.isBlank(flowPath.getFlowPathData())) {
                continue;
            }
            flowPathCount++;
            dealImagesAndSerialNum(flowPath, parent, directoryPath, serialNumBuilder, evaluationContentCount, flowPathCount);
        }
        return list;
    }

    private void dealImagesAndSerialNum(EmpiricalMaterialFlowPath flowPath, String parent, String directoryPath, StringBuilder serialNumBuilder, int evaluationContentCount, int flowPathCount) {
        serialNumBuilder.append("图")
                .append(flowPath.getDirectoryCode())
                .append("-")
                .append(evaluationContentCount)
                .append(".")
                .append(flowPathCount);
        flowPath.setSerialNum(serialNumBuilder.toString());
        String path;
        List<EMImagesInfo> base64List = new ArrayList<>();
        for (String fileName : flowPath.getFlowPathData().split(",")) {
            if (StrUtil.isBlank(fileName)) {
                continue;
            }
            path = parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + flowPath.getId() + "/" + fileName;
            if (!FileUtil.exist(path)) {
                log.error(path + "图片不存在！");
                continue;
            }
            //根据parent路径获取图片压缩后转换为Base64(某些接口不需要Base64图片，所以需要判断)
            if (StrUtil.isBlank(TransmittableThreadLocalManager.get())) {
                String base64 = com.jykj.dqm.utils.FileUtil.imageToBase64(path);
                base64List.add(EMImagesInfo.builder().fileName(fileName).base64Str(base64).build());
                flowPath.setImagesBase64List(base64List);
            }
        }
    }

    @Override
    public R queryAll(EmpiricalMaterialFlowPathQuery materialFlowPathQuery) {
        List<EmpiricalMaterialFlowPath> empiricalMaterialFlowPaths = new ArrayList<>();
        List<EmpiricalMaterialEvaluationContent> materialEvaluationContents =
                empiricalMaterialEvaluationContentService.list(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class)
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, materialFlowPathQuery.getDirectoryCode())
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, materialFlowPathQuery.getDirectoryName()));
        for (EmpiricalMaterialEvaluationContent materialEvaluationContent : materialEvaluationContents) {
            materialFlowPathQuery.setEvaluationContentId(materialEvaluationContent.getId());
            List<EmpiricalMaterialFlowPath> list = queryFlowPathInfo(materialFlowPathQuery, false);
            empiricalMaterialFlowPaths.addAll(list);
        }
        return RUtil.success(empiricalMaterialFlowPaths);
    }

    @Override
    public R save(EmpiricalMaterialFlowPathDTO empiricalMaterialFlowPathDTO) {
        List<EmpiricalMaterialFlowPath> empiricalMaterialFlowPaths = empiricalMaterialFlowPathDTO.getEmpiricalMaterialFlowPaths();
        if (CollUtil.isEmpty(empiricalMaterialFlowPaths)) {
            return RUtil.success();
        }
        empiricalMaterialFlowPaths.forEach(item -> {
            item.setProjectId(empiricalMaterialFlowPathDTO.getProjectId());
        });
        this.updateBatchById(empiricalMaterialFlowPaths);

        EmpiricalMaterialFlowPath empiricalMaterialFlowPath = empiricalMaterialFlowPaths.get(0);
        //更新状态：完成-》已分配
        empiricalMaterialTaskAllocationService.update(Wrappers.<EmpiricalMaterialTaskAllocation>lambdaUpdate()
                .set(EmpiricalMaterialTaskAllocation::getTaskStatus, "1")
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, empiricalMaterialFlowPath.getDirectoryCode())
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, empiricalMaterialFlowPath.getDirectoryName())
                .eq(EmpiricalMaterialTaskAllocation::getProjectId, empiricalMaterialFlowPath.getProjectId())
        );
        return RUtil.success();
    }

    @Override
    public R add(EmpiricalMaterialFlowPath materialFlowPath, List<MultipartFile> files) {
        //上传图片，将图片路径写到materialFlowPath
        String id = IdUtils.getID();
        materialFlowPath.setId(id);
        List<String> fileNames = uploadMultipleFiles(files, materialFlowPath.getId(), materialFlowPath.getProjectId(), materialFlowPath.getDirectoryName(), materialFlowPath.getDirectoryCode(), materialFlowPath.getEvaluationContentId());
        if (fileNames.size() > 0) {
            materialFlowPath.setFlowPathData(String.join(",", fileNames));
        }
        //保存materialFlowPath
        this.save(materialFlowPath);

        //更新状态：完成-》已分配
        empiricalMaterialTaskAllocationService.update(Wrappers.<EmpiricalMaterialTaskAllocation>lambdaUpdate()
                .set(EmpiricalMaterialTaskAllocation::getTaskStatus, "1")
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, materialFlowPath.getDirectoryCode())
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, materialFlowPath.getDirectoryName())
                .eq(EmpiricalMaterialTaskAllocation::getProjectId, materialFlowPath.getProjectId())
        );
        return RUtil.success(materialFlowPath);
    }

    @Override
    public R update(EmpiricalMaterialFlowPath materialFlowPath, List<MultipartFile> files) {
        //上传图片，将图片路径写到materialFlowPath
        List<String> fileNames = uploadMultipleFiles(files, materialFlowPath);
        if (fileNames.size() > 0) {
            materialFlowPath.setFlowPathData(String.join(",", fileNames));
        } else {
            materialFlowPath.setFlowPathData("");
        }
        //保存materialFlowPath
        this.updateById(materialFlowPath);

        //更新状态：完成-》已分配
        empiricalMaterialTaskAllocationService.update(Wrappers.<EmpiricalMaterialTaskAllocation>lambdaUpdate()
                .set(EmpiricalMaterialTaskAllocation::getTaskStatus, "1")
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, materialFlowPath.getDirectoryCode())
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, materialFlowPath.getDirectoryName())
                .eq(EmpiricalMaterialTaskAllocation::getProjectId, materialFlowPath.getProjectId())
        );
        return RUtil.success();
    }

    public List<String> uploadMultipleFiles(List<MultipartFile> files, EmpiricalMaterialFlowPath materialFlowPath) {
        files = files == null ? new ArrayList<>() : files;
        List<String> fileTypes = uploadImageFile.getFileTypes();
        //上传的时候类型获取为文件类型，就是点后面的内容
        Arrays.asList("jpeg", "png", "bmp", "icon", "jpg").forEach(fileTypes::add);
        for (MultipartFile file : files) {
            // 判断上传的文件类型
            String contentType = file.getContentType();
            if (CollUtil.isNotEmpty(fileTypes) && !fileTypes.contains(contentType)) {
                throw new BusinessException("不支持该类型的文件，允许的文件类型：" + fileTypes);
            }
        }
        String materialFlowPathId = materialFlowPath.getId();
        String directoryName = materialFlowPath.getDirectoryName();
        String directoryCode = materialFlowPath.getDirectoryCode();
        String evaluationContentId = materialFlowPath.getEvaluationContentId();
        List<String> deleteImageNames = materialFlowPath.getDeleteImageNames() == null ? new ArrayList<>() : materialFlowPath.getDeleteImageNames();
        List<String> fileNames = Arrays.stream(Optional.ofNullable(materialFlowPath.getFlowPathData()).orElse("")
                        .split(","))
                .filter(item -> !deleteImageNames.contains(item) && StrUtil.isNotBlank(item))
                .collect(Collectors.toList());
        //先拷贝目标路径文件，再操作原文件，如果操作顺利删除拷贝的文件，如果失败还原（需要加锁）
        String parent = SystemUtils.getFilePath();
        String directoryPath = directoryName + directoryCode + materialFlowPath.getProjectId() + "/" + evaluationContentId;
        String allPath = parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + materialFlowPathId;
        if (!FileUtil.exist(allPath)) {
            FileUtil.mkdir(allPath);
        }
        //删除原有图片
        for (String deleteImageName : deleteImageNames) {
            if (StrUtil.isBlank(deleteImageName)) {
                continue;
            }
            try {
                FileUtil.del(Paths.get(allPath + "/" + deleteImageName));
            } catch (Exception e) {
                log.warn(allPath + "/" + deleteImageName + ":" + e.getMessage());
            }
        }

        // 遍历文件列表并上传
        for (MultipartFile file : files) {
            try {
                Path targetDirectory = Paths.get(allPath);
                Path targetPath = targetDirectory.resolve(file.getOriginalFilename());
                File targetFile = targetPath.toFile();
                file.transferTo(targetFile);
                fileNames.add(file.getOriginalFilename());
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException("上传文件失败: " + file.getOriginalFilename());
            }
        }
        return fileNames.stream().distinct().collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R delete(EmpiricalMaterialFlowPath materialFlowPath) {
        if (StrUtil.isBlank(materialFlowPath.getDirectoryName()) || StrUtil.isBlank(materialFlowPath.getDirectoryCode()) || StrUtil.isBlank(materialFlowPath.getId())) {
            return RUtil.error("目录名称或目录编码或者ID为空");
        }
        String parent = SystemUtils.getFilePath();
        String directoryPath = materialFlowPath.getDirectoryName() + materialFlowPath.getDirectoryCode() + materialFlowPath.getProjectId();
        FileUtil.del(Paths.get(parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + materialFlowPath.getId()));
        this.removeById(materialFlowPath);
        //删除父节点时，删除子节点
        this.remove(Wrappers.lambdaQuery(EmpiricalMaterialFlowPath.class)
                .eq(EmpiricalMaterialFlowPath::getParentId, materialFlowPath.getId()));
        return RUtil.success();
    }

    @Override
    public void getImages(HttpServletResponse response, String fileName, String materialFlowPathId, String projectId, String directoryName, String directoryCode, String evaluationContentId) {
        String parent = SystemUtils.getFilePath();
        String directoryPath = directoryName + directoryCode + projectId + "/" + evaluationContentId;
        String path = parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + materialFlowPathId + "/" + fileName;
        File file = new File(path);
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;Filename=" + URLUtil.encode(fileName));
        response.setHeader("Content-Transfer-Encoding", "binary");
        try (FileInputStream fis = new FileInputStream(file); OutputStream os = response.getOutputStream()) {
            // 将文件写入response流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("获取失败：" + e.getMessage());
            throw new BusinessException("获取失败：" + e.getMessage());
        }
    }

    @Override
    public R markComplete(String directoryName, String directoryCode, String projectId) {
        List<EmpiricalMaterialEvaluationContent> materialEvaluationContents =
                empiricalMaterialEvaluationContentService.list(Wrappers.lambdaQuery(EmpiricalMaterialEvaluationContent.class)
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryCode, directoryCode)
                        .eq(EmpiricalMaterialEvaluationContent::getDirectoryName, directoryName));
        String parent = SystemUtils.getFilePath();
        String directoryPath;
        String path;
        //具体流程
        List<EmpiricalMaterialFlowPath> list;
        //检查能否标记完成
        for (EmpiricalMaterialEvaluationContent evaluationContent : materialEvaluationContents) {
            directoryPath = directoryName + directoryCode + projectId + "/" + evaluationContent.getId();
            list = this.list(Wrappers.lambdaQuery(EmpiricalMaterialFlowPath.class)
                    .eq(EmpiricalMaterialFlowPath::getDirectoryCode, directoryCode)
                    .eq(EmpiricalMaterialFlowPath::getDirectoryName, directoryName)
                    .eq(EmpiricalMaterialFlowPath::getProjectId, projectId)
                    .eq(EmpiricalMaterialFlowPath::getEvaluationContentId, evaluationContent.getId())
                    .eq(EmpiricalMaterialFlowPath::getFlowPathType, "2")
            );
            if (CollUtil.isEmpty(list)) {
                throw new BusinessException("没有流程，无法标记完成");
            }
            for (EmpiricalMaterialFlowPath flowPath : list) {
                if (StrUtil.isBlank(flowPath.getFlowPathData())) {
                    throw new BusinessException("有流程未上传图片，无法标记完成");
                }
                for (String fileName : flowPath.getFlowPathData().split(",")) {
                    path = parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + flowPath.getId() + "/" + fileName;
                    //根据parent路径获取图片压缩后转换为Base64
                    if (!FileUtil.exist(path)) {
                        throw new BusinessException("有流程未上传图片，无法标记完成");
                    }
                }
            }

        }
        empiricalMaterialTaskAllocationService.update(Wrappers.lambdaUpdate(EmpiricalMaterialTaskAllocation.class)
                .set(EmpiricalMaterialTaskAllocation::getTaskStatus, "2")
                .eq(EmpiricalMaterialTaskAllocation::getProjectId, projectId)
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, directoryCode)
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, directoryName));
        //异步执行导出实证材料文档
        empiricalMaterialExportService.asyncExportOneDirectory(directoryName, directoryCode, projectId);
        return RUtil.success();
    }

    @Override
    public List<String> uploadMultipleFiles(List<MultipartFile> files,
                                            @NotBlank String materialFlowPathId,
                                            @NotBlank String projectId,
                                            @NotBlank String directoryName,
                                            @NotBlank String directoryCode,
                                            @NotBlank String evaluationContentId) {
        List<String> fileNames = new ArrayList<>();
        if (files == null) {
            return fileNames;
        }
        // 目标目录
        // 获取上传文件的文件名
        String parent = SystemUtils.getFilePath();
        String directoryPath = directoryName + directoryCode + projectId + "/" + evaluationContentId;
        String allPath = parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + materialFlowPathId;
        Path targetDirectory = Paths.get(allPath + "temp");
        if (!FileUtil.exist(allPath)) {
            FileUtil.mkdir(allPath);
        }
        try {
            FileUtil.copyContent(Paths.get(allPath), targetDirectory, StandardCopyOption.REPLACE_EXISTING);
            // 遍历文件列表并上传
            for (MultipartFile file : files) {
                // 判断上传的文件类型
                String contentType = file.getContentType().toLowerCase(Locale.ENGLISH);
                List<String> fileTypes = uploadImageFile.getFileTypes();
                //上传的时候类型获取为文件类型，就是点后面的内容
                Arrays.asList("jpeg", "png", "bmp", "icon", "jpg").forEach(fileTypes::add);
                if (CollUtil.isNotEmpty(fileTypes) && !fileTypes.contains(contentType)) {
                    throw new BusinessException("不支持该类型的文件，允许的文件类型：" + fileTypes);
                }
                try {
                    Path targetPath = targetDirectory.resolve(file.getOriginalFilename());
                    File targetFile = targetPath.toFile();
                    file.transferTo(targetFile);
                    fileNames.add(file.getOriginalFilename());
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                    throw new BusinessException("上传文件失败: " + file.getOriginalFilename());
                }
            }
        } finally {
            FileUtil.del(Paths.get(parent + "/MaterialFlowPathFiles/" + directoryPath + "/" + materialFlowPathId));
            FileUtil.rename(targetDirectory, materialFlowPathId + "", true);
        }
        //更新状态：完成-》已分配
        empiricalMaterialTaskAllocationService.update(Wrappers.<EmpiricalMaterialTaskAllocation>lambdaUpdate()
                .set(EmpiricalMaterialTaskAllocation::getTaskStatus, "1")
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryCode, directoryCode)
                .eq(EmpiricalMaterialTaskAllocation::getDirectoryName, directoryName)
                .eq(EmpiricalMaterialTaskAllocation::getProjectId, projectId)
        );
        return fileNames;
    }
}