package com.jykj.dqm.emr.manager.generateword;

/**
 * 规则枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/10/31 9:25:48
 */
public enum RuleTypeEnum {
    WZX("WZX", "完整性", "1"),
    YZX("YZX", "一致性", "1"),
    JSX("JSX", "及时性", "1"),
    ZHX("ZHX", "整合性", "1");

    private String type;
    private String value;
    //告警系数
    private String alarmCoefficient;

    RuleTypeEnum(String type, String value, String alarmCoefficient) {
        this.type = type;
        this.value = value;
        this.alarmCoefficient = alarmCoefficient;
    }

    public String getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public String getAlarmCoefficient() {
        return alarmCoefficient;
    }
}
