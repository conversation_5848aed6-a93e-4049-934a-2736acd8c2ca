package com.jykj.dqm.emr.manager.generateword;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentExportRecordErrorDetail;
import com.jykj.dqm.emr.entity.DocumentRuleConfiguration;
import com.jykj.dqm.emr.manager.PictureUtils;
import com.jykj.dqm.emr.service.DocumentExportRecordErrorDetailService;
import com.jykj.dqm.emr.service.DocumentRuleSqlExecRecordService;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.metadata.entity.MetadataDatasource;
import com.jykj.dqm.system.entity.SysConfig;
import com.jykj.dqm.utils.DateTimeUtil;
import com.jykj.dqm.utils.RedisUtil;
import com.jykj.dqm.utils.StringUtil;
import com.jykj.dqm.utils.SysCodeContentUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 生成章节Word
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/28 14:05:14
 */
public abstract class GenerateChapterWord {
    @Autowired
    protected RedisTemplate redisTemplate;

    @Autowired
    protected DocumentExportRecordErrorDetailService documentExportRecordErrorDetailService;

    @Autowired
    protected DocumentRuleSqlExecRecordService documentRuleSqlExecRecordService;

    public static final String SYSTEM_SPLIT = "-";

    /**
     * 生成章节Word
     *
     * @param documentRuleConfigurations List<DocumentRuleConfiguration>
     * @param directoryConfiguration     DocumentDirectoryConfiguration
     * @param dataStartTime              dataStartTime
     * @param dataEndTime                dataEndTime
     * @param recordId                   recordId
     */
    void dealEachDoc(List<DocumentRuleConfiguration> documentRuleConfigurations, DocumentDirectoryConfiguration directoryConfiguration, String dataStartTime, String dataEndTime, String recordId, DocumentDirectoryConfiguration secondDirectoryConfiguration) {

    }

    /**
     * 规则类型
     *
     * @return 规则类型
     */
    RuleTypeEnum gainRuleRuleType() {
        return null;
    }

    /**
     * 将SQL中的时间占位符修改为具体时间
     *
     * @param sql       sql
     * @param dbType    数据库类型
     * @param startDate startDate
     * @param endDate   startDate
     * @return 补充时间后的SQL
     */
    String addTimeToSql(String sql, String dbType, String startDate, String endDate) {
        if (startDate.length() < 11) {
            startDate = startDate + " 00:00:00";
        }
        String startTimeSql = getSqlDateFunction(dbType, startDate);
        if (endDate.length() < 11) {
            endDate = endDate + " 23:59:59";
        }
        String endTimeSql = getSqlDateFunction(dbType, endDate);
        return sql.replace("#startDate", startTimeSql).replace("#endDate", endTimeSql);
    }

    /**
     * 获取日期装换方法
     *
     * @param dbType    数据库类型
     * @param dateValue 时间字段值
     * @return 日期装换方法
     * <AUTHOR>
     */
    public String getSqlDateFunction(String dbType, String dateValue) {
        String dateFun = "'" + dateValue + "'";
        if ("ORACLE".equals(dbType) || "TERADATA".equalsIgnoreCase(dbType)) {
            dateFun = "to_date('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        } else if ("MYSQL".equalsIgnoreCase(dbType)) {
            dateFun = "str_to_date('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        } else if ("SQLSERVER".equalsIgnoreCase(dbType)) {
            dateFun = "Cast('" + dateValue + "' as datetime)";
        } else if ("HIVE".equalsIgnoreCase(dbType)) {
            dateFun = "to_date('" + dateValue + "')";
        } else if ("PRESTO".equalsIgnoreCase(dbType)) {
            dateFun = "date_parse('" + dateValue + "'," + getDateFormatStyle(dbType, dateValue.length()) + ")";
        }
        return dateFun;
    }

    private String getDateFormatStyle(String dbType, int len) {
        String style = "";
        if ("ORACLE".equals(dbType) || "TERADATA".equals(dbType)) {
            style = "'yyyy-MM-dd hh24:mi:ss'";
            if (len < 11) {
                style = "'yyyy-MM-dd'";
            }
        } else if ("MYSQL".equalsIgnoreCase(dbType) || "PRESTO".equalsIgnoreCase(dbType)) {
            style = "'%Y-%m-%d %T'";
            if (len < 11) {
                style = "'%Y-%m-%d'";
            }
        }
        return style;
    }

    /**
     * 生成图片
     *
     * @param picturePath           picturePath
     * @param conditionalRecordsSql conditionalRecordsSql
     * @param metadataDatasource    metadataDatasource
     * @param match                 SQL执行结果（数字）
     */
    void generatePicture(String picturePath, String conditionalRecordsSql, MetadataDatasource metadataDatasource, long match) {
        PictureUtils pictureUtils = new PictureUtils();
        Map<String, String> params = new HashMap<>();
        params.put("dbName", metadataDatasource.getDatabaseName());
        params.put("sql", conditionalRecordsSql);
        params.put("result", match + "");
        params.put("datetime", DateTimeUtil.getNowDateTimeStr());
        pictureUtils.combinePicture(picturePath, params);
    }

    /**
     * 记录告警信息
     *
     * @param recordId
     * @param directoryCode
     * @param directoryName
     * @param emrRuleType
     * @param result
     */
    protected void recordRedisExceptionInfo(String recordId, String directoryCode, String directoryName, String emrRuleType, String result) {
        LambdaUpdateWrapper<DocumentExportRecordErrorDetail> errorDetailLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        errorDetailLambdaUpdateWrapper.eq(DocumentExportRecordErrorDetail::getExportRecordId, recordId)
                .eq(DocumentExportRecordErrorDetail::getDirectoryName, directoryName)
                .eq(DocumentExportRecordErrorDetail::getDirectoryCode, directoryCode)
                .eq(DocumentExportRecordErrorDetail::getEmrRuleType, emrRuleType);
        if (Double.parseDouble(SysCodeContentUtils.getAlarmCoefficientMap().get(RuleTypeEnum.WZX.getValue())) > Double.parseDouble(result) || 1.0 < Double.parseDouble(result)) {
            Map map = new HashMap();
            map.put("msg", result);
            map.put("directoryName", directoryName);
            map.put("directoryCode", directoryCode);
            map.put("emrRuleType", emrRuleType);

            redisTemplate.opsForList().leftPush(recordId + "_RESULT2", JSON.toJSONString(map));
            redisTemplate.expire(recordId + "_RESULT2", 10, TimeUnit.HOURS);

            //将告警记录入库
            DocumentExportRecordErrorDetail documentExportRecordErrorDetail =
                    DocumentExportRecordErrorDetail.builder().exportRecordId(recordId).errorReason(result).directoryCode(directoryCode).directoryName(directoryName).emrRuleType(emrRuleType).errorType("告警").build();
            documentExportRecordErrorDetailService.saveOrUpdate(documentExportRecordErrorDetail, errorDetailLambdaUpdateWrapper);
        } else {
            //删除
            documentExportRecordErrorDetailService.remove(errorDetailLambdaUpdateWrapper);
        }
    }

    /**
     * 组装表名和字段名
     *
     * @param table
     * @param field
     * @param tableAndFiledType
     * @return 表名.字段名
     */
    protected String getTableAndFieldName(String tableAndFiledType, String table, String field) {
        if ("1".equals(tableAndFiledType)) {
            //自定义
            return field;
        }
        if (StrUtil.isBlank(field)) {
            throw new BusinessException("字段不能为空");
        }
        if (StrUtil.isBlank(table)) {
            return field;
        }
        return table + "." + field;
    }

    protected String getDataSourceId(DocumentRuleConfiguration documentRuleConfiguration) {
        return "1".equals(documentRuleConfiguration.getWhetherCrossDbQuery()) ? documentRuleConfiguration.getCrossDbQueryDataSourceId() : documentRuleConfiguration.getDataSourceId();
    }

    protected String getDataSourceId2(DocumentRuleConfiguration documentRuleConfiguration) {
        return "1".equals(documentRuleConfiguration.getWhetherCrossDbQuery()) ? documentRuleConfiguration.getCrossDbQueryDataSourceId() : documentRuleConfiguration.getDataSourceId2();
    }

    protected boolean whetherProjectSystemTogether(List<DocumentRuleConfiguration> documentRuleConfigurations) {
        String configValue = RedisUtil.getSysConfigValue("emr.word.project.system.together", "N");
        return "Y".equals(configValue) && documentRuleConfigurations.get(0).getRequiredProject().contains(SYSTEM_SPLIT);
    }

    protected boolean hasRemark(List<DocumentRuleConfiguration> documentRuleConfigurations) {
        return StrUtil.isNotBlank(documentRuleConfigurations.get(0).getRuleConfigurationRemark());
    }

    /**
     * 获取Long类型的值，兼容不同的数据类型
     *
     * @param value 原始值
     * @return Long类型的值
     */
    protected long getLongValue(Object value) {
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }

    //按照SYSTEM_SPLIT对数据分组
    protected Map<String, List<DocumentRuleConfiguration>> groupDataBySystem(List<DocumentRuleConfiguration> documentRuleConfigurations, boolean projectSystemTogether) {
        Map<String, List<DocumentRuleConfiguration>> systemGroupMap = new LinkedHashMap<>();
        if (projectSystemTogether) {
            systemGroupMap = documentRuleConfigurations.stream().collect(Collectors.groupingBy(documentRuleConfiguration -> {
                String hospitalProjectTmp = documentRuleConfiguration.getHospitalProject();
                return hospitalProjectTmp.contains(SYSTEM_SPLIT) ? hospitalProjectTmp.substring(0, hospitalProjectTmp.indexOf(SYSTEM_SPLIT)) : "";
            }, LinkedHashMap::new, Collectors.toList()));
        } else {
            systemGroupMap.put("", documentRuleConfigurations);
        }
        return systemGroupMap;
    }

}
