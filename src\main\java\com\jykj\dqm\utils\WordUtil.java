package com.jykj.dqm.utils;

import cn.hutool.core.io.IoUtil;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import fr.opensagres.poi.xwpf.converter.xhtml.Base64EmbedImgManager;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;
import org.ofdrw.converter.ofdconverter.PDFConverter;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * WordUtils
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 23/3/30 12:29:32
 */
public class WordUtil {
    /**
     * docxToHtml
     *
     * @param inputStream InputStream
     * @param response    HttpServletResponse
     * @throws IOException
     */
    public static void docxToHtml(InputStream inputStream, HttpServletResponse response) throws IOException {
        XWPFDocument docxDocument = new XWPFDocument(inputStream);
        XHTMLOptions options = XHTMLOptions.create();
        options.setIgnoreStylesIfUnused(false);
        options.setFragment(true);
        // 图片转base64
        options.setImageManager(new Base64EmbedImgManager());
        // 转换htm1
        ByteArrayOutputStream htmlStream = new ByteArrayOutputStream();
        XHTMLConverter.getInstance().convert(docxDocument, htmlStream, options);
        // 清空response
        response.reset();
        OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        toClient.write(htmlStream.toByteArray());
        toClient.flush();
        toClient.close();
    }

    /**
     * docxToPDF
     *
     * @param inPath  输入文件路径
     * @param outPath 导出文件路径
     */
    public static void docxToPDF(String inPath, String outPath) {
        InputStream docx = null;
        OutputStream os = null;
        try {
            docx = new FileInputStream(inPath);
            XWPFDocument xwpfDocument = new XWPFDocument(docx);
            xwpfDocument.createStyles();
            PdfOptions pdf = PdfOptions.create();

            os = new FileOutputStream(outPath);
            PdfConverter.getInstance().convert(xwpfDocument, os, pdf);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(docx);
            IoUtil.close(os);
        }
    }

    /**
     * PDF转OFD
     *
     * @param inPath  PDF文件路径
     * @param outPath OFD输出路径
     * @throws Exception 转换过程中可能出现的异常
     */
    public static void pdfToOFD(String inPath, String outPath) throws Exception {
        try {
            Path pdfPath = Paths.get(inPath);
            Path ofdPath = Paths.get(outPath);

            // 使用PDFConverter进行转换，PDFConverter是DocConverter接口的一个实现类
            try (PDFConverter converter = new PDFConverter(ofdPath)) {
                converter.convert(pdfPath);
            }
        } catch (Exception e) {
            throw new Exception("PDF转OFD失败: " + e.getMessage(), e);
        }
    }

    public static void main(String[] args) throws Exception {
        //String inPath = "C:\\Users\\<USER>\\Desktop\\黄杰工作周报_20250526-20250601.doc";
        String inPath = "C:\\Users\\<USER>\\Desktop\\国家卫生健康委医政司关于征求智慧医疗分级评价方法及标准（2025版）意见的函.docx";
        String outPath = "C:\\Users\\<USER>\\Desktop\\testPdf11.ofd";
        // docxToPDF(inPath, outPath);
        String outPath2 = "C:\\Users\\<USER>\\Desktop\\testPdf11temp.pdf";
        docxToPDF(inPath, outPath2);
        String outPath3 = "C:\\Users\\<USER>\\Desktop\\testPdf200.ofd";
        pdfToOFD(outPath2, outPath3);
    }
}
