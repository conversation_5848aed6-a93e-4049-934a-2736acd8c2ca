package com.jykj.dqm.emr.manager;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.Includes;
import com.deepoove.poi.plugin.comment.CommentRenderPolicy;
import com.deepoove.poi.plugin.comment.Comments;
import com.deepoove.poi.util.RegexUtils;
import com.jykj.dqm.common.Constant;
import com.jykj.dqm.empiricalmaterial.entity.EmpiricalMaterialDirectory;
import com.jykj.dqm.emr.config.DetailTableCategoryMergePolicy;
import com.jykj.dqm.emr.config.TransmittableThreadLocalManager;
import com.jykj.dqm.emr.entity.DocumentDirectoryConfiguration;
import com.jykj.dqm.emr.entity.DocumentDirectoryFirst;
import com.jykj.dqm.emr.entity.DocumentDirectorySecond;
import com.jykj.dqm.emr.utils.PathNameUtils;
import com.jykj.dqm.exception.BusinessException;
import com.jykj.dqm.utils.HashCryptoFactory;
import com.jykj.dqm.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.LocaleUtil;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFStyles;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.jetbrains.annotations.NotNull;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTStyle;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTStyles;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVerticalJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblWidth;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class WordUtils {
    /**
     * List<Map<String, Object>> data = new ArrayList<>();
     * //标题行
     * Map<String, Object> map = new HashMap<>();
     * map.put("AAA", "姓名");
     * map.put("BBB", "年龄");
     * data.add(map);
     * <p>
     * //其他数据行
     * Map<String, Object> map2 = new HashMap<>();
     * map2.put("AAA", "张三");
     * map2.put("BBB", "31");
     * data.add(map2);
     * Map<String, Object> map3 = new HashMap<>();
     * map3.put("AAA", "王五");
     * map3.put("BBB", "29");
     * data.add(map3);
     * <p>
     * 生成表格
     *
     * @param data    List<Map<String, Object>>
     * @param docName 文件名称
     * <AUTHOR>
     */
    public void generateTable(List<Map<String, Object>> data, String docName) {
        XWPFTemplate template;
        InputStream templateInputStream = null;
        File docFile;
        try {
            String fileName = "formTemplate.docx";
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            ConfigureBuilder builder = Configure.builder();
            // 配置为SpEL模式
            builder.useSpringEL();
            // 除了标签前后缀外的任意字符
            builder.buildGrammerRegex(RegexUtils.createGeneral("{{", "}}"));
            builder.bind("targetRowData", new DetailTableCategoryMergePolicy());
            template = XWPFTemplate.compile(templateInputStream, builder.build());
            Map<String, Object> mapR = new HashMap<>();
            mapR.put("targetRowData", data);
            template.render(mapR);
            // 输出到文件
            // 获取项目目录的绝对路径
            // String docName = "src/main/resources/word/" + "table.docx";
            docName = docName + ".docx";
            docFile = new File(docName);
            template.writeToFile(docFile.getAbsolutePath());
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 生成表格（支持类别列合并）
     * 专门用于table1的类别列合并
     *
     * @param data    List<Map<String, Object>>
     * @param docName 文件名称
     * <AUTHOR>
     */
    public void generateTableWithCategoryMerge(List<Map<String, Object>> data, String docName) {
        XWPFTemplate template;
        InputStream templateInputStream = null;
        File docFile;
        try {
            String fileName = "formTemplate.docx";
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            ConfigureBuilder builder = Configure.builder();
            // 配置为SpEL模式
            builder.useSpringEL();
            // 除了标签前后缀外的任意字符
            builder.buildGrammerRegex(RegexUtils.createGeneral("{{", "}}"));
            // 使用支持类别列合并的策略
            builder.bind("targetRowData", new DetailTableCategoryMergePolicy());
            template = XWPFTemplate.compile(templateInputStream, builder.build());
            Map<String, Object> mapR = new HashMap<>();
            mapR.put("targetRowData", data);
            template.render(mapR);
            // 输出到文件
            docName = docName + ".docx";
            docFile = new File(docName);
            template.writeToFile(docFile.getAbsolutePath());
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 生成每个小章节
     * <p>
     * 章节编码（01.01.3）
     * 连接SQL跑规则
     * 类型（完整性、整合性、一致性、及时性）
     * 填充数据（Map<String, Object>）
     *
     * @param docNameBase 文件路径+名称
     * @param type        章节类型
     * @param params      参数
     */
    public void generateWordForEachChapter(String docNameBase, String type, Map<String, Object> params) {
        XWPFTemplate template = null;
        InputStream templateInputStream = null;
        try {
            String fileName = type + ".docx";
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/systemTogether/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            String userName = TransmittableThreadLocalManager.get();
            Comments.CommentBuilder commentBuilder = Comments
                    .of(params.get("directoryName") + "中共有" + params.get("allRecord") + "条记录");
            Configure config = Configure.builder().bind("comment", new CommentRenderPolicy()).useSpringEL().build();
            //去掉批注
            if ("ALL".equals(type)
                    && !NumberUtil.equals(Double.parseDouble(params.get("calculationFormulaResult") + ""), 1.0)) {
                commentBuilder.signature(userName, "s", LocaleUtil.getLocaleCalendar())
                        .comment("数据" + type + "存在差异");
            }
            params.put("comment", commentBuilder.create());
            // 替换模板中的内容
            template = XWPFTemplate.compile(templateInputStream, config).render(new HashMap<String, Object>() {
                {
                    put("table1", Includes.ofLocal(params.get("table1") + ".docx").create());
                    if (!"及时性".equals(type)) {
                        put("table2", Includes.ofLocal(params.get("table2") + ".docx").create());
                    }
                    put("showImage", params.get("showImage"));
                    put("sysData", params.get("sysData"));
                    put("dbType", params.get("dbType"));
                    put("dbType2", params.get("dbType2"));
                    put("monthNum", params.get("monthNum"));
                    put("directoryName", params.get("directoryName"));
                    put("allRequiredProject", params.get("allRequiredProject"));
                    put("sqlAll", params.get("sqlAll"));
                    put("sqlData", params.get("sqlData"));
                    put("dataStartTime", params.get("dataStartTime"));
                    put("dataEndTime", params.get("dataEndTime"));
                    put("levelCode", params.get("levelCode"));
                    put("tRange", params.get("tRange"));
                    put("allRecord", params.get("allRecord"));
                    put("calculationFormula1", params.get("calculationFormula1"));
                    put("calculationFormula2", params.get("calculationFormula2"));
                    put("calculationFormulaResult", params.get("calculationFormulaResult"));
                    put("nNum", params.get("nNum"));
                    put("comment", params.get("comment"));
                    put("pictureAll", params.get("pictureAll"));
                    put("problemDataRemarks1", params.get("problemDataRemarks1"));
                    put("problemDataRemarks2", params.get("problemDataRemarks2"));
                    put("problemDataRemarks3", params.get("problemDataRemarks3"));
                    put("problemDataRemarks4", params.get("problemDataRemarks4"));
                    put("remarks1", params.get("remarks1"));
                    put("remarks1e", params.get("remarks1e"));
                    put("remarks2", params.get("remarks2"));
                    put("remarks2e", params.get("remarks2e"));
                    put("remarks3", params.get("remarks3"));
                    put("remarks3e", params.get("remarks3e"));
                    if ("整合性".equals(type)) {
                        put("headerName1", params.get("headerName1"));
                        put("headerName2", params.get("headerName2"));
                    }
                }
            });
            // 输出到文件
            template.writeAndClose(new FileOutputStream(docNameBase + ".docx"));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 最后将生成的每个小节嵌套到主文件中
     *
     * @param templateFilePath 导出目录的文件：path
     * @param masterFile       主文件word
     * @param littleFiles      各个小节的word（placeholder：占位符，filePath：路径）
     * <AUTHOR>
     */
    public void mergeWordFiles(String templateFilePath, String masterFile, List<Map<String, String>> littleFiles,
            Map<String, String> params) {
        /*
         * XWPFTemplate template =
         * XWPFTemplate.compile("C:/Users/<USER>/Desktop/llll.docx").render(new
         * HashMap<String, Object>() {
         * {
         * put("zwf1", Includes.ofLocal("C:/Users/<USER>/Desktop/ex3.docx").create());
         * put("zwf2", Includes.ofLocal("C:/Users/<USER>/Desktop/ex3.docx").create());
         * put("zwf3", Includes.ofLocal("C:/Users/<USER>/Desktop/ex3.docx").create());
         * }
         * });
         * template.writeAndClose(new
         * FileOutputStream("C:/Users/<USER>/Desktop/output3333.docx"));
         */

        ConfigureBuilder builder = Configure.builder();
        builder.buildGrammerRegex(RegexUtils.createGeneral("{{", "}}"));
        builder.useDefaultEL(false);

        String token = params.get("token");
        XWPFTemplate template = XWPFTemplate.compile(templateFilePath + "template.docx").render(new LinkedHashMap() {
            {
                put("exportRecordId", params.get("exportRecordId"));
                put("token", token);
                for (Map<String, String> littleFile : littleFiles) {
                    put(littleFile.get("placeholder"), Includes.ofLocal(littleFile.get("filePath")).create());
                }
            }
        });

        try {
            template.writeAndClose(new FileOutputStream(templateFilePath + "templateWithCompletedValues.docx"));
            FileUtil.del(masterFile);

            InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("word/电子病历评级文档.docx");
            XWPFTemplate template2 = XWPFTemplate.compile(resourceAsStream).render(new HashMap<String, Object>() {
                {
                    put("year", params.get("year"));
                    put("levelCode", params.get("levelCode"));
                    put("yearAndMouth", params.get("yearAndMouth"));
                    put("hospitalCode", params.get("hospitalCode"));
                    put("hospitalName", params.get("hospitalName"));
                    put("eachChapter",
                            Includes.ofLocal(templateFilePath + "templateWithCompletedValues.docx").create());
                }
            });
            template2.writeAndClose(new FileOutputStream(masterFile));
            Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
            if (processMap != null) {
                // 记录进度
                processMap.put(token,
                        MapUtil.builder(new HashMap<String, Object>()).put(params.get("exportRecordId"), 0.99).build());
            }
        } catch (Exception e) {
            Map<String, Map<String, Object>> processMap = TransmittableThreadLocalManager.processMap;
            if (processMap != null) {
                processMap.put(token,
                        MapUtil.builder(new HashMap<String, Object>()).put(params.get("exportRecordId"), -1.0).build());
            }
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 生成文档目录模板
     *
     * @param documentDirectorys
     * @param pathPath
     * @param params
     */
    public void generateCatalogDocumentTemplate(List<DocumentDirectoryFirst> documentDirectorys, String pathPath,
            Map<String, String> params) {
        InputStream templateInputStream = null;
        try {
            // 获得模板文档的整体样式
            // 新建的word文档对象
            XWPFDocument doc = new XWPFDocument();
            // word整体样式
            // 读取模板文档
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/formatnew.docx");
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            XWPFDocument template = new XWPFDocument(templateInputStream);
            // 获取源文档中的所有样式，创建 XWPFStyles 的实例
            XWPFStyles sourceStyles = template.getStyles();

            // 获取目标文档中的所有样式，创建 XWPFStyles 的实例
            XWPFStyles targetStyles = doc.createStyles();
            // 遍历源文档中的所有样式，使用 XWPFStyles 的 addStyle (XWPFStyle style) 方法将其添加到目标文档中
            CTStyles style = template.getStyle();
            CTStyle[] styleArray = style.getStyleArray();
            for (CTStyle ctStyle : styleArray) {
                targetStyles.addStyle(sourceStyles.getStyle(ctStyle.getStyleId()));
            }

            for (DocumentDirectoryFirst documentDirectoryFirst : documentDirectorys) {
                // 标题1，1级大纲
                dealFirstTitle(doc, documentDirectoryFirst.getDirectoryName());
                // 标题2，2级大纲
                for (DocumentDirectorySecond secondLevel : documentDirectoryFirst.getSecondLevels()) {
                    dealSecondTitle(doc, secondLevel);
                }
            }
            // word写入到文件
            FileOutputStream fos = new FileOutputStream(pathPath + "template.docx");
            doc.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("生成模板文件失败", e);
        }
    }

    /**
     * 三级标题和正文
     * 信息利用
     * 10.01.3临床数据整合
     * 完整性
     * 1
     * 2
     * 一致性
     * 1
     * 2
     *
     * @param doc
     * @param documentDirectorySecond
     */
    private void dealThirdTitleAndText(XWPFDocument doc, DocumentDirectorySecond documentDirectorySecond) {
        Map<String, List<DocumentDirectoryConfiguration>> thirdLevels = documentDirectorySecond.getThirdLevels();
        for (Map.Entry<String, List<DocumentDirectoryConfiguration>> entry : thirdLevels.entrySet()) {
            // 标题3，3级大纲
            XWPFParagraph para3 = doc.createParagraph();
            // 关键行// 3级大纲
            setStyle(para3, "4", 1, 200, 200);
            XWPFRun run3 = para3.createRun();
            run3.setFontFamily("宋体");
            run3.setFontSize(11);
            // 标题内容
            // run3.setText("01.01.3 医嘱记录");
            if (entry.getValue().size() == 1) {
                DocumentDirectoryConfiguration directoryConfiguration = entry.getValue().get(0);
                run3.setText(entry.getKey() + " " + directoryConfiguration.getDirectoryName());
                // 正文 单独启一行，解决表格跑到最前面
                writeTheText(doc,
                        directoryConfiguration.getDirectoryCode()
                                + HashCryptoFactory.encrypt(directoryConfiguration.getDirectoryName()) + "_"
                                + directoryConfiguration.getEmrRuleType());
                continue;
            }
            int index = 0;
            for (DocumentDirectoryConfiguration directoryConfiguration : entry.getValue()) {
                if (index == 0) {
                    run3.setText(entry.getKey());
                }
                // 标题4 4级大纲
                XWPFParagraph para4 = doc.createParagraph();
                // 关键行// 3级大纲
                setStyle(para4, "5", 1, 200, 200);
                XWPFRun run4 = para4.createRun();
                run4.setFontFamily("宋体");
                run4.setFontSize(11);
                // 标题内容
                run4.setText(directoryConfiguration.getDirectoryName());
                // 正文
                writeTheText(doc,
                        directoryConfiguration.getDirectoryCode()
                                + HashCryptoFactory.encrypt(directoryConfiguration.getDirectoryName()) + "_"
                                + directoryConfiguration.getEmrRuleType());
                index++;
            }
        }
    }

    private void writeTheText(XWPFDocument doc, String text) {
        // 正文 单独启一行，解决表格跑到最前面
        XWPFParagraph para5 = doc.createParagraph();
        XWPFRun run5 = para5.createRun();
        run5.setFontFamily("宋体");
        run5.setFontSize(11);
        // run4.setText("{{+01.01.3医嘱记录}}");
        run5.setText("{{+" + text + "}}");
    }

    /**
     * 二级标题
     *
     * @param doc
     * @param documentDirectorySecond
     */
    private void dealSecondTitle(XWPFDocument doc, DocumentDirectorySecond documentDirectorySecond) {
        // 标题2，2级大纲
        XWPFParagraph para2 = doc.createParagraph();
        // 关键行// 2级大纲
        setStyle(para2, "3", 1, 200, 200);
        XWPFRun run2 = para2.createRun();
        // 标题内容
        // run2.setText("10.01.4临床数据整合");
        run2.setText(documentDirectorySecond.getDirectoryCode() + documentDirectorySecond.getDirectoryName());
        run2.setFontFamily("宋体");
        run2.setFontSize(14);
        // 数据
        // 处理数据质量考察项目
        Map<String, List<DocumentDirectoryConfiguration>> thirdLevels = documentDirectorySecond.getThirdLevels();
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<DocumentDirectoryConfiguration>> entry : thirdLevels.entrySet()) {
            sb.append(entry.getKey()).append("::");
            for (DocumentDirectoryConfiguration documentDirectoryConfiguration : entry.getValue()) {
                if (entry.getValue().size() > 1) {
                    sb.append(Constant.LINE_SEPARATOR);
                    sb.append(documentDirectoryConfiguration.getDirectoryName());
                } else {
                    sb.append(documentDirectoryConfiguration.getDirectoryName());
                }
            }
            sb.append(Constant.LINE_SEPARATOR);
        }
        // sb去掉最后的换行符
        String dataQualityCheckProject = sb.toString().replaceAll(Constant.LINE_SEPARATOR + "$", "");
        List<Map<String, Object>> targetRowData = Arrays.asList(
                MapUtil.builder(new LinkedHashMap<String, Object>()).put("one", "序号").put("two", "项目代码")
                        .put("three", "业务项目").put("four", "数据质量考察项目").build(),
                MapUtil.builder(new LinkedHashMap<String, Object>())
                        .put("one", "1")
                        .put("two", documentDirectorySecond.getDirectoryCode())
                        .put("three", documentDirectorySecond.getDirectoryName())
                        .put("four", dataQualityCheckProject)
                        .build());
        createTable(doc, targetRowData);
        // 标题3，3级大纲
        dealThirdTitleAndText(doc, documentDirectorySecond);
    }

    private void createTable(XWPFDocument doc, List<Map<String, Object>> targetRowData) {
        // 创建表格
        XWPFTable table = doc.createTable();
        table.removeRow(0);
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);
        // 8503DXA=15CM
        tblPr.getTblW().setW(new BigInteger("9735"));
        for (int i = 0; i < targetRowData.size(); i++) {
            // 第一行是标题行
            XWPFTableRow xwpfTableRow = table.insertNewTableRow(i);
            xwpfTableRow.setHeight(450);
            // 循环列 row-cell
            int index = 0;
            for (Map.Entry vo : targetRowData.get(i).entrySet()) {
                index++;
                XWPFTableCell cell = xwpfTableRow.createCell();
                // if (i == 0) {
                // // 背景色
                // cell.setColor("00ff00");
                // }
                CTTcPr tcpr = cell.getCTTc().addNewTcPr();
                CTTblWidth cellw = tcpr.addNewTcW();
                cellw.setType(STTblWidth.DXA);
                // 设置垂直居中
                if (i == 0) {
                    CTVerticalJc verticalJc = tcpr.addNewVAlign();
                    verticalJc.setVal(STVerticalJc.CENTER);
                }

                if (index == 1) {
                    cellw.setW(BigInteger.valueOf(680 * 1));
                } else if (index == 2) {
                    cellw.setW(BigInteger.valueOf(560 * 2));
                } else if (index == 3) {
                    cellw.setW(BigInteger.valueOf(1804)); //3.18*567
                } else if (index == 4) {
                    cellw.setW(BigInteger.valueOf(9735 - 680 * 1 - 560 * 2 - 1804));
                }
                XWPFParagraph p = cell.getParagraphs().get(0);
                // 文字靠左位置
                if (i == 0) {
                    p.setAlignment(ParagraphAlignment.CENTER);
                } else {
                    p.setAlignment(ParagraphAlignment.LEFT);
                }
                // 单元格赋值
                // 手动换行
                String string = vo.getValue().toString();
                String[] split = new String[] { string };
                if (string.contains(Constant.LINE_SEPARATOR)) {
                    split = string.split(Constant.LINE_SEPARATOR);
                } else if (string.contains("\n")) {
                    split = string.split("\n");
                }

                for (int j = 0; j < split.length; j++) {
                    String value = split[j];
                    if (value.endsWith("::")) {
                        XWPFRun r1 = getXwpfRun(p, i, j, true);
                        r1.setText(value.replaceAll("::", ":"));
                    } else if (value.contains("::")) {
                        String[] split1 = value.split("::");
                        XWPFRun r1 = getXwpfRun(p, i, j, true);
                        r1.setText(split1[0] + ":");
                        XWPFRun r2 = getXwpfRun(p, i, j, false, false);
                        r2.setText(split1[1]);
                    } else {
                        XWPFRun r = getXwpfRun(p, i, j, false);
                        r.setText(value);
                    }
                }
            }
        }
    }

    @NotNull
    private XWPFRun getXwpfRun(XWPFParagraph p, int i, int j, boolean isBold) {
        XWPFRun r = p.createRun();
        if (i == 0 || isBold) {
            r.setBold(true);
        }
        if (j > 0) {
            r.addBreak();
        }
        r.setFontFamily("宋体");
        r.setFontSize(11);
        return r;
    }

    @NotNull
    private XWPFRun getXwpfRun(XWPFParagraph p, int i, int j, boolean isBold, boolean addBreak) {
        XWPFRun r = p.createRun();
        if (i == 0 || isBold) {
            r.setBold(true);
        }
        if (j > 0 && addBreak) {
            r.addBreak();
        }
        r.setFontFamily("宋体");
        r.setFontSize(11);
        return r;
    }

    /**
     * 一级标题
     *
     * @param doc
     * @param directoryName
     */
    private void dealFirstTitle(XWPFDocument doc, String directoryName) {
        // 标题1，1级大纲
        XWPFParagraph para1 = doc.createParagraph();
        // 关键行// 1级大纲
        setStyle(para1, "2");
        XWPFRun run1 = para1.createRun();
        // 标题内容
        // run1.setText("一 病房医师");
        run1.setText(directoryName);
        run1.setFontFamily("宋体");
        run1.setFontSize(18);
    }

    /**
     * 设置字体样式
     *
     * @param para  XWPFParagraph
     * @param style 1、2、3 1级大纲，2级大纲，3级大纲 可以看到heading 1对应的styleId是1，heading
     *              2对应的styleId是2,heading 1和heading2又是什么鬼？其实就是对应的目录栏,heading
     *              1是正文，heading 2是标题1
     */
    private void setStyle(XWPFParagraph para, String style) {
        para.setStyle(style);
        para.setSpacingBetween(1);
        para.setSpacingBefore(0);
        para.setSpacingAfter(0);
    }

    /**
     * 设置字体样式
     *
     * @param para  XWPFParagraph
     * @param style 1、2、3 1级大纲，2级大纲，3级大纲 可以看到heading 1对应的styleId是1，heading
     *              2对应的styleId是2,heading 1和heading2又是什么鬼？其实就是对应的目录栏,heading
     *              1是正文，heading 2是标题1
     */
    private void setStyle(XWPFParagraph para, String style, double spacingBetween, int spacingBefore,
            int spacingAfter) {
        para.setStyle(style);
        para.setSpacingBetween(spacingBetween);
        para.setSpacingBefore(spacingBefore);
        para.setSpacingAfter(spacingAfter);
    }

    /**
     * 生成每个小章节的评价内容
     * 填充数据（Map<String, Object>）
     *
     * @param pathDocName 文件路径+名称
     * @param params      参数
     */
    public void generateEmWordForEachEvaluationContent(String pathDocName, Map<String, Object> params) {
        XWPFTemplate template = null;
        InputStream templateInputStream = null;
        try {
            // 判断是否横向排版，并获取模板文件名
            String fileName = PathNameUtils.getEmpiricalmaterialFileName("emEachContent.docx");
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            // 替换模板中的内容
            template = XWPFTemplate.compile(templateInputStream).render(new HashMap<String, Object>() {
                {
                    put("flowContentData", params.get("flowContentData"));
                    put("flowPictureData", params.get("flowPictureData"));
                }
            });
            // 输出到文件
            template.writeAndClose(new FileOutputStream(pathDocName));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    public void generateEmWordForEachChapter(String pathDocName, Map<String, Object> params) {
        XWPFTemplate template = null;
        InputStream templateInputStream = null;
        try {
            // 判断是否横向排版，并获取模板文件名
            String fileName = PathNameUtils.getEmpiricalmaterialFileName("emDict.docx");
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            // 替换模板中的内容
            template = XWPFTemplate.compile(templateInputStream).render(new HashMap<String, Object>() {
                {
                    put("number", params.get("number"));
                    put("directoryCode", params.get("directoryCode"));
                    put("workRole", params.get("workRole"));
                    put("project", params.get("project"));
                    put("evaluationType", params.get("evaluationType"));
                    put("evaluationContent", params.get("evaluationContent"));
                    put("tableDesc", params.get("tableDesc"));
                    put("evaluationcontentData", params.get("evaluationcontentData"));
                }
            });
            // 输出到文件
            template.writeAndClose(new FileOutputStream(pathDocName));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    public void generateEmWordForEachChapterComplete(String templateFile, String pathDocName,
            Map<String, Object> params) {
        XWPFTemplate template = null;
        InputStream templateInputStream = null;
        try {
            String normal = templateFile.replaceAll("horizontal/", "");
            if (!normal.contains("/") && !normal.contains("\\")) {
                templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + templateFile);
                if (null == templateInputStream) {
                    throw new BusinessException("模板文件不存在");
                }
                template = XWPFTemplate.compile(templateInputStream).render(params);
            } else {
                template = XWPFTemplate.compile(templateFile).render(params);
            }
            // 输出到文件
            template.writeAndClose(new FileOutputStream(pathDocName));
        } catch (Exception e) {
            throw new RuntimeException(pathDocName + "生成失败：" + e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 报错将错误信息，写入Word中
     *
     * @param exception Exception
     * @param path      path
     */
    public void dealEmErrorDoc(Exception exception, String path) {
        XWPFTemplate template;
        InputStream templateInputStream = null;
        try {
            String fileName = "EM错误.docx";
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/" + fileName);
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            // 替换模板中的内容
            template = XWPFTemplate.compile(templateInputStream).render(new HashMap<String, Object>() {
                {
                    put("failureResult",
                            "预览文件失败，请修改正确，重新点击完成！错误原因：" + System.lineSeparator() + StringUtil.getStackTrace(exception));
                }
            });
            // 输出到文件
            template.writeAndClose(new FileOutputStream(path));
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e);
        } finally {
            IoUtil.close(templateInputStream);
        }
    }

    /**
     * 生成文档目录模板
     *
     * @param configurationList
     * @param pathPath
     * @param params
     */
    public void generateEmCatalogDocumentTemplate(List<EmpiricalMaterialDirectory> configurationList, String pathPath,
            Map<String, String> params) {
        InputStream templateInputStream = null;
        try {
            // 获得模板文档的整体样式
            // 新建的word文档对象
            XWPFDocument doc = new XWPFDocument();
            // word整体样式
            // 读取模板文档
            templateInputStream = this.getClass().getClassLoader().getResourceAsStream("word/format.docx");
            if (null == templateInputStream) {
                throw new BusinessException("模板文件不存在");
            }
            XWPFDocument template = new XWPFDocument(templateInputStream);
            // 获取源文档中的所有样式，创建 XWPFStyles 的实例
            XWPFStyles sourceStyles = template.getStyles();

            // 获取目标文档中的所有样式，创建 XWPFStyles 的实例
            XWPFStyles targetStyles = doc.createStyles();
            // 遍历源文档中的所有样式，使用 XWPFStyles 的 addStyle (XWPFStyle style) 方法将其添加到目标文档中
            CTStyles style = template.getStyle();
            CTStyle[] styleArray = style.getStyleArray();
            for (CTStyle ctStyle : styleArray) {
                targetStyles.addStyle(sourceStyles.getStyle(ctStyle.getStyleId()));
            }
            Set<String> set = new HashSet<>();
            // 去掉没有子目录的父目录
            for (EmpiricalMaterialDirectory directory : configurationList) {
                if (directory.getDirectoryCode().length() < 7) {
                    continue;
                }
                set.add(directory.getDirectoryCode().substring(0, 2));
                set.add(directory.getDirectoryCode().substring(0, 5));
                set.add(directory.getDirectoryCode());
            }

            for (EmpiricalMaterialDirectory directoryConfiguration : configurationList) {
                if (!set.contains(directoryConfiguration.getDirectoryCode())) {
                    continue;
                }
                if (directoryConfiguration.getDirectoryCode().length() == 2) {
                    dealEmFirstTitle(doc, directoryConfiguration);
                } else if (directoryConfiguration.getDirectoryCode().length() >= 7) {
                    dealEmThirdTitleAndText(doc, directoryConfiguration);
                }
            }
            // word写入到文件
            FileOutputStream fos = new FileOutputStream(pathPath);
            doc.write(fos);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("生成模板文件失败", e);
        }
    }

    /**
     * 三级标题和正文
     *
     * @param doc
     * @param directoryConfiguration
     */
    private void dealEmThirdTitleAndText(XWPFDocument doc, EmpiricalMaterialDirectory directoryConfiguration) {
        // 标题3，3级大纲
        XWPFParagraph para3 = doc.createParagraph();
        // 关键行// 3级大纲
        setStyle(para3, "2");
        XWPFRun run3 = para3.createRun();
        run3.setFontFamily("宋体");
        run3.setFontSize(14);
        // 标题内容
        // run3.setText("01.01.3 医嘱记录");
        StringBuffer text = new StringBuffer();
        text.append(directoryConfiguration.getDirectoryCode()).append(directoryConfiguration.getDirectoryName());
        run3.setText(text.toString());

        // 正文 单独启一行，解决表格跑到最前面
        XWPFParagraph para4 = doc.createParagraph();
        XWPFRun run4 = para4.createRun();
        run4.setFontFamily("宋体");
        run4.setFontSize(11);
        // run4.setText("{{+01.01.3医嘱记录}}");
        run4.setText("{{+" + text + "}}");
    }

    /**
     * 一级标题
     *
     * @param doc
     * @param directoryConfiguration
     */
    private void dealEmFirstTitle(XWPFDocument doc, EmpiricalMaterialDirectory directoryConfiguration) {
        // 标题1，1级大纲
        XWPFParagraph para1 = doc.createParagraph();
        // 关键行// 1级大纲
        setStyle(para1, "1");
        XWPFRun run1 = para1.createRun();
        // 标题内容
        // run1.setText("一 病房医师");
        run1.setText(directoryConfiguration.getDirectoryName());
        run1.setFontFamily("宋体");
        run1.setFontSize(18);
    }

    /**
     * 二级标题
     *
     * @param doc
     * @param directoryConfiguration
     */
    private void dealEmSecondTitle(XWPFDocument doc, EmpiricalMaterialDirectory directoryConfiguration) {
        // 标题2，2级大纲
        XWPFParagraph para2 = doc.createParagraph();
        // 关键行// 2级大纲
        setStyle(para2, "2");

        XWPFRun run2 = para2.createRun();
        // 标题内容
        // run2.setText("1、病房医嘱处理");
        run2.setText(directoryConfiguration.getSerialNum() + "、" + directoryConfiguration.getDirectoryName());
        run2.setFontFamily("宋体");
        run2.setFontSize(14);
    }

}
