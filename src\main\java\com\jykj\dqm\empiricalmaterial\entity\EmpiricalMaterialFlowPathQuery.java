package com.jykj.dqm.empiricalmaterial.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 实证材料流程和资料
 *
 * @TableName DQM_EMR_EMPIRICAL_MATERIAL_FLOW_PATH
 */
@Accessors(chain = true)
@Data
public class EmpiricalMaterialFlowPathQuery implements Serializable {
    @NotBlank
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 目录名称
     */
    @NotBlank
    @ApiModelProperty(value = "目录名称")
    private String directoryName;

    /**
     * 目录编码
     */
    @NotBlank
    @ApiModelProperty(value = "目录编码")
    private String directoryCode;

    /**
     * 评价内容Id
     */
    @ApiModelProperty(value = "评价内容ID")
    private String evaluationContentId;
}
